package jp.co.torihada.fanme.modules.fanme.controllers

import BaseController
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditStatus
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditType
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType
import jp.co.torihada.fanme.modules.fanme.services.audit.AuditGroupServiceAuditObject
import jp.co.torihada.fanme.modules.fanme.usecases.audit.CreateAuditGroup
import jp.co.torihada.fanme.modules.fanme.usecases.audit.UpdateAuditStatus

data class AuditGroupControllerAuditObject(
    val bucket: String,
    val filePath: String,
    val assetType: AssetType,
)

data class AuditGroupControllerCreateInput(
    val userUid: String,
    val operationType: String,
    val metadata: AuditGroupMetadata,
    val auditObjects: List<AuditGroupControllerAuditObject>,
)

data class AuditGroupControllerUpdateStatusInput(
    val auditGroupId: Long,
    val status: AuditStatus,
    val comment: String? = null,
    val auditedUserUid: String? = null,
)

@ApplicationScoped
class AuditGroupController : BaseController() {

    @Inject private lateinit var createAuditGroup: CreateAuditGroup
    @Inject private lateinit var updateAuditStatus: UpdateAuditStatus

    /** ショップ監査情報の作成 */
    @Transactional(Transactional.TxType.REQUIRES_NEW)
    fun createShopAuditGroup(input: AuditGroupControllerCreateInput): Long {
        return createAuditGroup.execute(
            userUid = input.userUid,
            auditType = AuditType.SHOP,
            operationType = AuditGroup.OperationType.fromValue(input.operationType),
            metadata = input.metadata,
            auditObjects =
                input.auditObjects.map {
                    AuditGroupServiceAuditObject(
                        bucket = it.bucket,
                        filePath = it.filePath,
                        assetType = it.assetType,
                    )
                },
        )
    }

    /** 監査ステータスの更新 */
    @Transactional(Transactional.TxType.REQUIRES_NEW)
    fun updateAuditStatus(input: AuditGroupControllerUpdateStatusInput) {
        updateAuditStatus.execute(
            auditGroupId = input.auditGroupId,
            status = input.status,
            comment = input.comment,
            auditedUserUid = input.auditedUserUid,
        )
    }
}
