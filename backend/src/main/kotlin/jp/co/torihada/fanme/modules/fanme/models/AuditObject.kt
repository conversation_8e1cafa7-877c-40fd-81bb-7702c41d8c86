package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "audit_objects")
class AuditObject : BaseModel() {
    enum class AssetType(val value: String) {
        IMAGE("IMAGE"), // 画像
        VOICE("VOICE"), // 音声
        MOVIE("MOVIE"), // 動画
        BENEFIT("BENEFIT"); // 特典

        companion object {
            fun fromValue(value: String): AssetType {
                return values().find { it.value == value }
                    ?: throw IllegalArgumentException("Invalid AssetType value: $value")
            }
        }
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audit_group_id", insertable = false, updatable = false)
    var auditGroup: AuditGroup? = null

    @NotNull @Column(name = "audit_group_id", nullable = false) var auditGroupId: Long = 0

    @Size(max = 50) @NotNull @Column(name = "bucket", nullable = false) var bucket: String = ""

    @Size(max = 255)
    @NotNull
    @Column(name = "file_path", nullable = false)
    var filePath: String = ""

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "asset_type", nullable = false)
    var assetType: AssetType = AssetType.IMAGE

    companion object : PanacheCompanion<AuditObject> {
        fun findByAuditGroupId(auditGroupId: Long): List<AuditObject> {
            return find("auditGroupId", auditGroupId).list()
        }
    }
}
