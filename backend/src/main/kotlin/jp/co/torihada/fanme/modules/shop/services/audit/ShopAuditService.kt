package jp.co.torihada.fanme.modules.shop.services.audit

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupController
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupControllerAuditObject
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupControllerCreateInput
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType
import jp.co.torihada.fanme.modules.shop.models.Shop
import org.jboss.logging.Logger

@ApplicationScoped
class ShopAuditService {
    enum class OperationType(val value: String) {
        INSERT("INSERT"),
        UPDATE("UPDATE"),
    }

    @Inject private lateinit var logger: Logger

    @Inject private lateinit var auditGroupController: AuditGroupController

    @Inject private lateinit var bucketPathResolver: BucketPathResolver

    fun createAuditDataForShop(shop: Shop, creatorUid: String, operationType: OperationType) {
        try {
            if (
                shop.headerImageUri !== null &&
                    shop.headerImageUri!!.isNotEmpty() &&
                    !bucketPathResolver.isValidPath(shop.headerImageUri!!)
            ) {
                return
            }

            val auditObjects = mutableListOf<AuditGroupControllerAuditObject>()
            shop.headerImageUri?.let {
                auditObjects.add(
                    AuditGroupControllerAuditObject(
                        bucket = bucketPathResolver.getBucketName(it),
                        filePath = bucketPathResolver.getFilePath(it),
                        assetType = AssetType.IMAGE,
                    )
                )
            }

            auditGroupController.createShopAuditGroup(
                AuditGroupControllerCreateInput(
                    userUid = creatorUid,
                    operationType = operationType.value,
                    metadata =
                        AuditGroupMetadata(
                            shopId = shop.id,
                            title = shop.name,
                            description = shop.description,
                        ),
                    auditObjects = auditObjects,
                )
            )
        } catch (e: Exception) {
            // 作成されなくても動作するリソースのため、エラーを無視する
            logger.error("Failed to create shop limitation", e)
            logger.error(e.stackTraceToString())
        }
    }
}
