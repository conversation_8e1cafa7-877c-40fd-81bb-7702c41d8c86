package jp.co.torihada.fanme.modules.shop.usecases.shop

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceAlreadyExistsException
import jp.co.torihada.fanme.modules.shop.models.Shop
import jp.co.torihada.fanme.modules.shop.models.ShopLimitation
import jp.co.torihada.fanme.modules.shop.services.audit.ShopAuditService
import org.jboss.logging.Logger

@ApplicationScoped
class CreateShop {

    @Inject private lateinit var logger: Logger

    @Inject private lateinit var shopAuditService: ShopAuditService

    data class Input(
        val creatorUid: String,
        val name: String,
        val description: String?,
        val headerImageUri: String?,
        val message: String?,
    )

    fun execute(params: Input): Result<Shop, FanmeException> {
        var shop = Shop.findByCreatorUid(params.creatorUid)
        if (shop != null) {
            return Err(ResourceAlreadyExistsException("Shop"))
        }
        shop =
            Shop.create(
                params.creatorUid,
                params.name,
                params.description,
                params.headerImageUri,
                params.message,
            )

        try {
            ShopLimitation.create(shop.id!!)
        } catch (e: Exception) {
            // 作成されなくても動作するリソースのため、エラーを無視する
            logger.error("Failed to create shop limitation", e)
            logger.error(e.stackTraceToString())
        }

        // 監査情報を作成
        shopAuditService.createAuditDataForShop(
            shop,
            params.creatorUid,
            ShopAuditService.OperationType.INSERT,
        )

        return Ok(shop)
    }
}
