package jp.co.torihada.fanme.modules.shop.usecases.shop

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.shop.models.Shop
import jp.co.torihada.fanme.modules.shop.services.audit.ShopAuditService
import org.jboss.logging.Logger

@ApplicationScoped
class UpdateShop {
    @Inject private lateinit var logger: Logger

    @Inject private lateinit var shopAuditService: ShopAuditService

    data class Input(
        val creatorUid: String,
        val name: String,
        val description: String?,
        val headerImageUri: String?,
        val message: String?,
    )

    fun execute(params: Input): Result<Shop, FanmeException> {
        try {
            val shop =
                Shop.update(
                    params.creatorUid,
                    params.name,
                    params.description,
                    params.headerImageUri,
                    params.message,
                )

            // 監査情報を作成
            shopAuditService.createAuditDataForShop(
                shop,
                params.creatorUid,
                ShopAuditService.OperationType.UPDATE,
            )

            return Ok(shop)
        } catch (e: FanmeException) {
            return Err(e)
        } catch (e: Exception) {
            return Err(FanmeException(code = 0, message = e.message ?: "Unknown error"))
        }
    }
}
