package jp.co.torihada.fanme.modules.fanme.services.audit

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.*
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test

@QuarkusTest
class AuditGroupServiceTest {

    @Inject lateinit var auditGroupService: AuditGroupService

    // テスト用データ
    private val testUserUid = "test-user"
    private val testMetadata =
        AuditGroupMetadata(shopId = 123L, title = "テスト商品", description = "テスト商品の説明")
    private val testAuditObjects =
        listOf(
            AuditGroupServiceAuditObject(
                bucket = "test-bucket",
                filePath = "test/image.jpg",
                assetType = AssetType.IMAGE,
            )
        )

    @Test
    @TestTransaction
    fun `createShopAuditGroupはSHOP型のcreateAuditGroupに処理を委譲しデータベースに保存する`() {
        // テスト実行
        val auditGroupId =
            auditGroupService.createShopAuditGroup(
                userUid = testUserUid,
                operationType = OperationType.INSERT,
                metadata = testMetadata,
                auditObjects = testAuditObjects,
            )

        // データベースから作成されたエンティティを取得して検証
        val createdGroup = AuditGroup.findById(auditGroupId)
        assertNotNull(createdGroup)
        assertEquals(testUserUid, createdGroup?.userUid)
        assertEquals(AuditType.SHOP, createdGroup?.auditType)
        assertEquals(OperationType.INSERT, createdGroup?.operationType)
        assertEquals(AuditStatus.UNAUDITED, createdGroup?.status)

        // メタデータの検証
        val savedMetadata =
            createdGroup?.metadata?.let { jacksonObjectMapper().readValue<AuditGroupMetadata>(it) }
        assertEquals(testMetadata.shopId, savedMetadata?.shopId)
        assertEquals(testMetadata.title, savedMetadata?.title)
        assertEquals(testMetadata.description, savedMetadata?.description)

        // 関連する監査オブジェクトの検証
        val auditObjects = AuditObject.findByAuditGroupId(auditGroupId)
        assertEquals(1, auditObjects.size)
        val auditObject = auditObjects[0]
        assertEquals(testAuditObjects[0].bucket, auditObject.bucket)
        assertEquals(testAuditObjects[0].filePath, auditObject.filePath)
        assertEquals(testAuditObjects[0].assetType, auditObject.assetType)
    }

    @Test
    @TestTransaction
    fun `updateAuditStatusはステータスを正しく更新する`() {
        // テスト用のAuditGroupを作成
        val auditGroupId =
            auditGroupService.createAuditGroup(
                userUid = testUserUid,
                auditType = AuditType.SHOP,
                operationType = OperationType.INSERT,
                metadata = testMetadata,
                auditObjects = testAuditObjects,
            )

        // ステータス更新
        val newStatus = AuditStatus.APPROVED
        val comment = "承認コメント"
        val auditorId = "auditor-user"

        auditGroupService.updateAuditStatus(
            auditGroupId = auditGroupId,
            status = newStatus,
            comment = comment,
            auditedUserUid = auditorId,
        )

        // 更新後のAuditGroupを取得して検証
        val updatedGroup = AuditGroup.findById(auditGroupId)
        assertEquals(newStatus, updatedGroup?.status)
        assertEquals(comment, updatedGroup?.comment)
        assertEquals(auditorId, updatedGroup?.auditedUserUid)
        assertNotNull(updatedGroup?.auditedAt) // 日時が設定されていることを確認
    }
}
