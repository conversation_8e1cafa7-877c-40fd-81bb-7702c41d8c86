package jp.co.torihada.fanme.modules.shop.services.audit

import io.quarkus.test.junit.QuarkusTest
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupController
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupControllerAuditObject
import jp.co.torihada.fanme.modules.fanme.controllers.AuditGroupControllerCreateInput
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType
import jp.co.torihada.fanme.modules.shop.models.Shop
import jp.co.torihada.fanme.modules.shop.services.audit.ShopAuditService.OperationType
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.times
import org.mockito.MockitoAnnotations

@QuarkusTest
class ShopAuditServiceTest {
    @InjectMocks private lateinit var shopAuditService: ShopAuditService

    @Mock private lateinit var auditGroupController: AuditGroupController

    @Mock private lateinit var bucketPathResolver: BucketPathResolver

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
    }

    @Test
    fun `ヘッダー画像なしのショップの監査データが正しく作成されること`() {
        // Given
        val shop =
            Shop().apply {
                id = 1L
                name = "テストショップ"
                description = "テスト説明"
                creatorUid = "test-uid"
                headerImageUri = null
                message = "テストメッセージ"
            }

        val input =
            AuditGroupControllerCreateInput(
                userUid = shop.creatorUid!!,
                operationType = OperationType.INSERT.value,
                metadata =
                    AuditGroupMetadata(
                        shopId = shop.id,
                        title = shop.name,
                        description = shop.description,
                    ),
                auditObjects = emptyList(),
            )
        Mockito.`when`(auditGroupController.createShopAuditGroup(input)).thenReturn(1L)

        // When
        shopAuditService.createAuditDataForShop(shop, shop.creatorUid!!, OperationType.INSERT)

        // Then
        Mockito.verify(auditGroupController, times(1)).createShopAuditGroup(input)
    }

    @Test
    fun `ヘッダー画像ありのショップの監査データが正しく作成されること`() {
        // Given
        val url = "https://s3-domain.com/test-bucket/test-path/image.jpg"
        val shop =
            Shop().apply {
                id = 1L
                name = "テストショップ"
                description = "テスト説明"
                creatorUid = "test-uid"
                headerImageUri = url
            }
        Mockito.`when`(bucketPathResolver.isValidPath(url)).thenReturn(true)
        Mockito.`when`(bucketPathResolver.getBucketName(url)).thenReturn("test-bucket")
        Mockito.`when`(bucketPathResolver.getFilePath(url)).thenReturn("test-path/image.jpg")

        val input =
            AuditGroupControllerCreateInput(
                userUid = shop.creatorUid!!,
                operationType = OperationType.UPDATE.value,
                metadata =
                    AuditGroupMetadata(
                        shopId = shop.id,
                        title = shop.name,
                        description = shop.description,
                    ),
                auditObjects =
                    listOf(
                        AuditGroupControllerAuditObject(
                            bucket = "test-bucket",
                            filePath = "test-path/image.jpg",
                            assetType = AssetType.IMAGE,
                        )
                    ),
            )
        Mockito.`when`(auditGroupController.createShopAuditGroup(input)).thenReturn(1L)

        // When
        shopAuditService.createAuditDataForShop(shop, shop.creatorUid!!, OperationType.UPDATE)

        // Then
        Mockito.verify(auditGroupController, times(1)).createShopAuditGroup(input)
    }

    @Test
    fun `ヘッダー画像のURIが不正な場合でもエラーをスローせずに処理を継続すること`() {
        // Given
        val shop =
            Shop().apply {
                id = 1L
                name = "テストショップ"
                description = "テスト説明"
                creatorUid = "test-uid"
                headerImageUri = "invalid-uri"
            }
        Mockito.`when`(bucketPathResolver.isValidPath("invalid-uri")).thenReturn(false)

        // When
        shopAuditService.createAuditDataForShop(shop, shop.creatorUid!!, OperationType.INSERT)

        // Then
        // エラー時はskipするので、処理は通ってokとする
    }
}
