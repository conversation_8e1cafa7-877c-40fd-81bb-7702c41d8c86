import toast from 'react-hot-toast';
import { blurImageData } from '@kayahr/stackblur';
import CustomToast from '@/components/atoms/toast/custom-toast';
import { ITEM_FILENAME_MAX_LENGTH } from '@/consts/inputLength';
import { MAX_BLOB_SIZE } from '@/consts/sizes';
import { fileService } from '@/services/file';
import { compressImage } from '@/utils/base';
import { getS3KeyFromUrl } from '@/utils/file';
import { MediaType, SingleItem, ItemFiles } from '@/types/shopItem';

// Progress bar is divided into 3 phases for better UX:
// 1. Compression : Show progress during image compression
// 2. Upload : Main upload progress to S3
// 3. Blur : Final processing for thumbnail generation
const UPLOAD_PROGRESS_PHASES = {
  COMPRESSION: {
    START: 0,
    END: 10,
  },
  UPLOAD: {
    START: 10,
    END: 90,
  },
  BLUR: {
    START: 90,
    END: 100,
  },
} as const;

// ユーティリティ関数：dataURLをBlobに変換する
const dataURLToBlob = async (dataurl: string, mimeType?: string) => {
  //@ts-ignore
  const type = mimeType || dataurl.match(/data:(.+);/) ? dataurl.match(/data:(.+);/)[1] : '';

  const base64 = dataurl.split(',')[1];
  const binStr = atob(base64);
  const u8a = new Uint8Array(binStr.length);
  let p = binStr.length;

  while (p) {
    p--;
    //@ts-ignore
    u8a[p] = binStr.codePointAt(p);
  }

  // Blobの生成
  let blob = new Blob([u8a], { type: mimeType || type });

  // 画像タイプかつサイズが500KBより大きい場合は圧縮を試みる
  if (type.startsWith('image/') && blob.size > MAX_BLOB_SIZE) {
    blob = await compressImage(blob);
  }

  return blob;
};

const anyUrlToBlob = async (url: string): Promise<Blob> => {
  //　data:imageを処理する
  if (url.startsWith('data:')) {
    return dataURLToBlob(url);
  }

  // http(s)、ローカルファイルURLを処理する
  try {
    const response = await fetch(url, { cache: 'no-store' });
    return await response.blob();
  } catch (error) {
    console.error('URL to Blob conversion failed:', error);
    throw error;
  }
};

const fileToBase64 = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

// ユーティリティ関数：遅延実行
const sleep = (waitSeconds: number): Promise<void> => {
  return new Promise((resolve) => {
    setTimeout(resolve, waitSeconds);
  });
};

// 画像ファイルを処理する
const handleImage = async (mediaItem: SingleItem, uploadUrl: string, originalUrl?: string): Promise<SingleItem> => {
  const newMediaItem = { ...mediaItem }; // readOnlyのためコピーを生成して更新
  if (uploadUrl) {
    newMediaItem.src = originalUrl || uploadUrl;
    newMediaItem.thumbnail = uploadUrl;
    return newMediaItem;
  }
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      newMediaItem.src = e.target?.result as string;
      newMediaItem.thumbnail = mediaItem.src;
      resolve(newMediaItem);
    };
    reader.readAsDataURL(newMediaItem.file as Blob);
  });
};
// 動画ファイルを処理する
const handleVideo = async (mediaItem: SingleItem, uploadUrl: string, isPublic?: boolean): Promise<SingleItem> => {
  const newMediaItem = { ...mediaItem }; // readOnlyのためコピーを生成して更新
  const localUrl = URL.createObjectURL(mediaItem.file as Blob);
  const video = document.createElement('video');
  const canvas = document.createElement('canvas');

  video.src = localUrl;
  video.preload = 'auto';
  video.muted = true;
  video.autoplay = true;
  video.currentTime = 1;
  video.crossOrigin = 'anonymous';
  video.style.position = 'fixed';
  video.style.zIndex = '-1';
  video.load();
  try {
    await new Promise<void>((resolve, reject) => {
      video.addEventListener('loadedmetadata', () => {
        video.onseeked = () => resolve();
      });
      video.onerror = reject;
    });
    await sleep(500);
    video.pause();
    const ctx = canvas.getContext('2d');

    if (ctx) {
      // 動画のアスペクト比を計算する
      const videoRatio =
        video.videoWidth > video.videoHeight
          ? video.videoWidth / video.videoHeight
          : video.videoHeight / video.videoWidth;
      const isWgth = video.videoWidth > video.videoHeight;

      // サムネイルを生成する (750x750)
      canvas.width = 750;
      canvas.height = 750;
      const videoWidth = isWgth ? canvas.width * videoRatio : canvas.width;
      const videoHeight = isWgth ? canvas.height : canvas.height * videoRatio;
      ctx.drawImage(video, 0, 0, videoWidth, videoHeight);
      const videoThumbnail = canvas.toDataURL('image/jpeg');

      const videoThumbnailUrl = await uploadThumbnail({
        img: videoThumbnail,
        fileId: mediaItem.id,
        isPublic: isPublic,
      });

      newMediaItem.thumbnail = videoThumbnailUrl.url;

      // フルサイズのサムネイルを生成する
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      const newFile = canvas.toDataURL('image/jpeg');
      const compressedFile = (await compressImage(newFile as unknown as File)) as File;
      newMediaItem.fullThumbnail = compressedFile as unknown as string;
    }

    newMediaItem.duration = video.duration;
    newMediaItem.src = uploadUrl ? uploadUrl : localUrl;

    return newMediaItem;
  } finally {
    setTimeout(() => {
      URL.revokeObjectURL(localUrl);
      video.remove();
      canvas.remove();
    }, 1000);
  }
};

// オーディオファイルを処理する
const handleAudio = async (mediaItem: SingleItem, uploadUrl: string): Promise<SingleItem> => {
  const newMediaItem = { ...mediaItem }; // readOnlyのためコピーを生成して更新
  const localUrl = URL.createObjectURL(mediaItem.file as Blob);
  const audio = document.createElement('audio');

  audio.src = localUrl;
  audio.preload = 'auto';

  try {
    await new Promise<void>((resolve, reject) => {
      audio.addEventListener('loadedmetadata', () => {
        resolve();
      });
      audio.onerror = reject;
    });
    newMediaItem.duration = audio.duration;
    newMediaItem.src = uploadUrl ? uploadUrl : localUrl;
    newMediaItem.thumbnail = '/shop/images/voice.png';
    return newMediaItem;
  } catch (error) {
    console.error(error);
    return newMediaItem;
  } finally {
    setTimeout(() => {
      URL.revokeObjectURL(localUrl);
      audio.remove();
    }, 1000);
  }
};

// メディアサムネイルを生成するメイン関数
// - 前処理（ローディングを表示するためのsrc=loadingのSingleItemを生成する）
const generatePreMediaThumbnail = (file: File): SingleItem => {
  const fileType = file.type.split('/')[0] as MediaType;
  return {
    file,
    type: fileType,
    src: '',
    thumbnail: '',
    title: file.name.replace(/\.[^/.]+$/, '').slice(0, ITEM_FILENAME_MAX_LENGTH), // 拡張子を除去
    id: `${file.name}-${Date.now()}`, // ユニークなIDを日付を足すことで生成
    size: file.size,
    isLoading: true,
  };
};
// - アップロード処理が終われば実際のsrcを更新する
const generateMediaThumbnail = async (
  mediaItem: SingleItem,
  fileUrl: string,
  originalUrl?: string,
  isPublic?: boolean,
): Promise<SingleItem> => {
  const newMediaItem = { ...mediaItem };
  newMediaItem.isLoading = false;
  switch (mediaItem.type) {
    case 'image':
      return await handleImage(newMediaItem, fileUrl, originalUrl);
    case 'video':
      return await handleVideo(newMediaItem, fileUrl, isPublic);
    case 'audio':
      return await handleAudio(newMediaItem, fileUrl);
    default:
      throw new Error('Unsupported media type');
  }
};

// ファイルアップロードを処理する関数
const handleMediaUpload = async (
  files: FileList,
  currentFiles: ItemFiles,
  callback: (files: ItemFiles) => void,
  onProgress?: (id: string, progress: number) => void,
  isPublic?: boolean,
) => {
  // isPublicはtrueの場合は→全部公開にする、falseの場合は→サムネイルだけ公開
  const updatedFiles = [...currentFiles];
  const uploadProgress: { [key: string]: number } = {};
  const mediaItems = await Promise.all(
    Array.from(files).map(async (file) => {
      try {
        if (file.type.includes('image')) {
          /*************************************
           * 画像のアップロード処理（必要な場合のみ圧縮処理を行う）
           *************************************/
          // ローディング表示のためSingleItemをコールバック
          const preItem = generatePreMediaThumbnail(file);
          updatedFiles.push(preItem);
          if (callback) callback(updatedFiles);

          const progressInterval = setInterval(() => {
            uploadProgress[preItem.id] = (uploadProgress[preItem.id] || 0) + 0.5;
            onProgress?.(
              preItem.id,
              Math.min(
                UPLOAD_PROGRESS_PHASES.COMPRESSION.END,
                UPLOAD_PROGRESS_PHASES.COMPRESSION.START + uploadProgress[preItem.id],
              ),
            );
          }, 100);

          // 画像を圧縮し、可能であればwebpに変換
          const uploadedFile = (await compressImage(file, true)) as File;
          clearInterval(progressInterval);

          let fileUrl = '';
          let uploadedFileUrl = '';

          try {
            fileUrl = URL.createObjectURL(file);
            uploadedFileUrl = URL.createObjectURL(uploadedFile);
            // 実際にアップロード処理を行う（圧縮した画像のみをアップロード）
            const uploadUrlCompressed = await fileService.uploadFile({
              file: uploadedFile,
              metadataList: [{ id: preItem.id, name: null }],
              isPublic: isPublic,
              onProgress: (progress) => {
                const progressRange = UPLOAD_PROGRESS_PHASES.UPLOAD.END - UPLOAD_PROGRESS_PHASES.UPLOAD.START;
                onProgress?.(preItem.id, UPLOAD_PROGRESS_PHASES.UPLOAD.START + (progress * progressRange) / 100);
              },
            });

            // blur処理（90-100%）
            onProgress?.(preItem.id, UPLOAD_PROGRESS_PHASES.BLUR.START);
            const result = await generateMediaThumbnail(preItem, uploadUrlCompressed, uploadUrlCompressed, isPublic);
            onProgress?.(preItem.id, UPLOAD_PROGRESS_PHASES.BLUR.END);
            return result;
          } catch (error) {
            toast.custom((t) => CustomToast(t, 'error', '画像アップロードに失敗しました'), {
              id: `upload-error-${preItem.id}`,
            });
            throw error;
          } finally {
            // 元の画像とメモリ上の圧縮画像を破棄
            if (fileUrl) URL.revokeObjectURL(fileUrl);
            if (uploadedFileUrl) URL.revokeObjectURL(uploadedFileUrl);
          }
        } else {
          /*************************************
           * 画像以外のアップロード処理
           *************************************/
          // ファイルのローディング表示のためSingleItemをコールバック
          const preItem = generatePreMediaThumbnail(file);
          updatedFiles.push(preItem);
          if (callback) callback(updatedFiles);
          // 実際にアップロード処理を行う
          const uploadUrl = await fileService.uploadFile({
            file: file,
            metadataList: [{ id: preItem.id, name: null }],
            isPublic: isPublic,
            onProgress: (progress) => onProgress?.(preItem.id, progress),
          });
          return generateMediaThumbnail(preItem, uploadUrl, undefined, isPublic);
        }
      } catch (error) {
        console.error('Error processing file:', error);
        toast.custom((t) => CustomToast(t, 'error', '商品アップロードに失敗しました'));
        throw error;
      }
    }),
  );

  return [...currentFiles, ...mediaItems];
};

const uploadThumbnail = async (data: {
  img: string | File;
  fileId: string;
  fileName?: string;
  existUploadUrl?: string;
  isCover?: boolean;
  isPublic?: boolean;
}) => {
  try {
    let thumbnailFile = data.img;
    if (typeof data.img === 'string') {
      const blob = await anyUrlToBlob(data.img);

      thumbnailFile = new File([blob], data.fileName || `temp_${data.fileId}`, { type: blob.type });
    }
    const uploadUrl = data.existUploadUrl
      ? data.existUploadUrl
      : await fileService
          .getUploadUrl({
            metadataList: [{ id: data.fileId, name: data.fileName || null }],
            isCover: data.isCover,
            isPublic: data.isPublic,
          })
          .then((res) => res.url);

    if (!uploadUrl) {
      throw new Error('Failed to get upload URL');
    }
    await fileService.uploadFileToS3(uploadUrl, thumbnailFile as File);
    return { id: data.fileId, uploadUrl, url: uploadUrl.split('?')[0] };
  } catch (error) {
    console.error(error);
    throw error;
  }
};

const generateProcessedImageString = async (src: string, blurLevel: number, watermarkLevel: number) => {
  const canvas = document.createElement('canvas');
  try {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 画像を読み込む
    const img = new window.Image();
    img.crossOrigin = 'anonymous';
    img.src = src;
    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
    });

    // --- 画像のリサイズ処理 ---
    const size = Math.min(img.width, img.height);
    const sx = (img.width - size) / 2;
    const sy = (img.height - size) / 2;
    const targetSize = 352;
    canvas.width = targetSize;
    canvas.height = targetSize;

    // **ぼかし用の CSS フィルタ適用**
    ctx.drawImage(img, sx, sy, size, size, 0, 0, targetSize, targetSize);

    const radius = 10 * blurLevel;
    const imageData = ctx.getImageData(0, 0, targetSize, targetSize);
    blurImageData(imageData, radius, true);
    ctx.putImageData(imageData, 0, 0);

    if (watermarkLevel === 0) {
      return canvas.toDataURL('image/jpeg');
    }

    // **ウォーターマークを適用**
    const watermarkImg = new window.Image();
    watermarkImg.crossOrigin = 'anonymous';
    watermarkImg.src =
      watermarkLevel === 1 ? '/shop/images/icons/setting/MaskWhite.svg' : '/shop/images/icons/setting/MaskBlack.svg';
    await new Promise((resolve, reject) => {
      watermarkImg.onload = resolve;
      watermarkImg.onerror = reject;
    });

    ctx.drawImage(watermarkImg, 0, 0, targetSize, targetSize);

    return canvas.toDataURL('image/jpeg');
  } catch (error) {
    console.error('Error generating processed thumbnail:', error);
  } finally {
    canvas.remove();
  }
};

const generateProcessedThumbnail = async (
  mediaItem: SingleItem,
  blurLevel: number,
  watermarkLevel: number,
): Promise<SingleItem> => {
  const newMediaItem = { ...mediaItem };
  if (!mediaItem.thumbnail || mediaItem.type == 'audio' || !mediaItem.preSignedThumbnailUrl) return newMediaItem;
  const processedThumbnailStr = await generateProcessedImageString(
    mediaItem.preSignedThumbnailUrl,
    blurLevel,
    watermarkLevel,
  );
  if (processedThumbnailStr) {
    const blob = await anyUrlToBlob(processedThumbnailStr);
    const thumbnailFile = new File([blob], mediaItem.title, { type: blob.type });
    newMediaItem.blur = blurLevel;
    newMediaItem.watermark = watermarkLevel;
    newMediaItem.processedThumbnail = URL.createObjectURL(thumbnailFile);
  }
  return newMediaItem;
};

const generatePreSignedThumbnails = async (mediaItems: SingleItem[], identityId: string) => {
  // 署名が必要なファイル
  const filesNeedToBeSigned = mediaItems.filter(
    (file) => !!file.thumbnail && file.type !== 'audio' && !isPublicBucketUrl(file.thumbnail),
  );
  let preSignedUrlsMap: Record<string, string> = {};
  if (filesNeedToBeSigned.length > 0) {
    try {
      const preSignedUrls = await fileService.getPreSignedUrl(
        identityId,
        filesNeedToBeSigned.map((file) => ({
          id: file.id,
          key: getS3KeyFromUrl(file.thumbnail!),
        })),
      );
      preSignedUrlsMap = Object.fromEntries(preSignedUrls.map(({ id, url }) => [id, url]));
    } catch (error) {
      console.error('Error fetching presigned URLs:', error);
    }
  }
  return mediaItems.map((file) => ({
    ...file,
    // サムネイルがパブリックになっている場合はpreSignedThumbnailUrlにそのままurlをセット
    preSignedThumbnailUrl:
      file.type === 'audio' ? '/shop/images/voice.png' : preSignedUrlsMap[file.id] || file.thumbnail,
  }));
};

const isPublicBucketUrl = (url: string) => {
  try {
    const parsedUrl = new URL(url);
    const pathSegments = parsedUrl.pathname.split('/').filter(Boolean);
    return pathSegments.includes('public');
  } catch {
    return false;
  }
};

export {
  generateMediaThumbnail,
  generateProcessedThumbnail,
  handleMediaUpload,
  dataURLToBlob,
  anyUrlToBlob,
  uploadThumbnail,
  fileToBase64,
  generatePreMediaThumbnail,
  generatePreSignedThumbnails,
  generateProcessedImageString,
};
